'use client';

import { User } from 'firebase/auth';

// Enhanced authentication utilities for admin panel
export interface AuthUser {
  uid: string;
  email: string;
  displayName?: string;
  isAdmin: boolean;
  photoURL?: string;
}

// Preview access email whitelist - these emails are allowed to access the admin panel
const getPreviewAccessEmails = (): string[] => {
  if (typeof window !== 'undefined') {
    // Try new naming convention first, fallback to old for backward compatibility
    const previewEmails = process.env.NEXT_PUBLIC_PREVIEW_ACCESS_EMAILS || process.env.NEXT_PUBLIC_ADMIN_EMAILS || '';
    return previewEmails.split(',')
      .map(email => email.trim())
      .filter(email => email.length > 0 && isValidEmail(email));
  }
  return [];
};

// Validate email format
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const hasPreviewAccess = (email: string): boolean => {
  if (!email || !isValidEmail(email)) {
    return false;
  }

  const previewAccessEmails = getPreviewAccessEmails();
  return previewAccessEmails.includes(email.toLowerCase());
};

export const createAuthUser = (firebaseUser: User): AuthUser => {
  return {
    uid: firebaseUser.uid,
    email: firebaseUser.email || '',
    displayName: firebaseUser.displayName || undefined,
    isAdmin: hasPreviewAccess(firebaseUser.email || ''),
    photoURL: firebaseUser.photoURL || undefined,
  };
};

export const setAuthToken = async (token: string): Promise<void> => {
  try {
    // Set HTTP-only cookie via API call
    await fetch('/api/auth/set-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token }),
    });
  } catch (error) {
    console.error('Failed to set auth token:', error);
  }
};

export const clearAuthToken = async (): Promise<void> => {
  try {
    // Clear HTTP-only cookie via API call
    await fetch('/api/auth/clear-token', {
      method: 'POST',
    });
  } catch (error) {
    console.error('Failed to clear auth token:', error);
  }
};

export const redirectToMainSite = (): void => {
  const mainDomain = process.env.NEXT_PUBLIC_MAIN_DOMAIN || 'blocksconnect.com';
  window.location.href = `https://${mainDomain}`;
};

export const logout = async (): Promise<void> => {
  try {
    // Clear server-side token
    await clearAuthToken();

    // Clear any client-side storage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_user');
      sessionStorage.clear();
    }
  } catch (error) {
    console.error('Logout error:', error);
  }
};
