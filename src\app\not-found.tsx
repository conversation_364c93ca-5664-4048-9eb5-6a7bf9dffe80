'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function NotFound() {
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleGoHome = () => {
    router.push('/');
  };

  const handleGoBack = () => {
    router.back();
  };

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4 md:px-8 relative overflow-hidden">
      {/* Decorative background elements */}
      <div className="decorative-blob decorative-blob-1"></div>
      <div className="decorative-blob decorative-blob-2"></div>
      <div className="decorative-blob decorative-blob-3"></div>

      <div className="w-full max-w-2xl mx-auto text-center space-y-8 fade-in-up">
        {/* 404 Icon */}
        <div className="flex justify-center mb-8">
          <div className="relative">
            <div className="w-32 h-32 rounded-full flex items-center justify-center relative overflow-hidden card glow">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20"></div>
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                className="h-16 w-16 text-blue-400 relative z-10" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={1.5} 
                  d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" 
                />
              </svg>
            </div>
            {/* Floating particles around the icon */}
            <div className="absolute -top-2 -left-2 w-3 h-3 bg-blue-400 rounded-full opacity-60 animate-pulse"></div>
            <div className="absolute -bottom-2 -right-2 w-2 h-2 bg-purple-400 rounded-full opacity-60 animate-pulse delay-300"></div>
            <div className="absolute top-1/2 -right-4 w-2 h-2 bg-blue-300 rounded-full opacity-40 animate-pulse delay-500"></div>
          </div>
        </div>

        {/* Error Code */}
        <div className="space-y-4">
          <h1 className="text-8xl md:text-9xl font-black gradient-text tracking-tight">
            404
          </h1>
          <div className="h-1 w-24 mx-auto bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
        </div>

        {/* Error Message */}
        <div className="space-y-4 fade-in-up delay-200">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Page Not Found
          </h2>
          <p className="text-lg md:text-xl text-gray-300 font-light max-w-lg mx-auto leading-relaxed">
            Oops! The page you're looking for seems to have wandered off into the digital void. 
            Don't worry, even the best explorers sometimes take a wrong turn.
          </p>
          <h3 className="text-lg font-semibold text-white mb-4">Maybe you we're looking for something else?</h3>
          <ul className="space-y-2 text-gray-300">
            <li className="hover:text-white transition-colors">
              <a href="/dashboard">Dashboard</a>
            </li>
            <li className="hover:text-white transition-colors">
              <a href="/minecraft/dashboard">Minecraft Dashboard</a>
            </li>
            <li className="hover:text-white transition-colors">
              <a href="https://your-nextcloud-domain.example.com" target="_blank" rel="noopener noreferrer">File Manager (Nextcloud)</a>
            </li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8 fade-in-up delay-400">
          <button
            onClick={handleGoHome}
            className="btn-primary px-8 py-4 text-lg font-semibold flex items-center gap-3 min-w-[200px] justify-center"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-5 w-5" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" 
              />
            </svg>
            Go Home
          </button>
          
          <button
            onClick={handleGoBack}
            className="btn-secondary px-8 py-4 text-lg font-semibold flex items-center gap-3 min-w-[200px] justify-center"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-5 w-5" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M10 19l-7-7m0 0l7-7m-7 7h18" 
              />
            </svg>
            Go Back
          </button>
        </div>

        {/* Additional Help */}
        <div className="pt-8 fade-in-up delay-500">
          <div className="card p-6 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-white mb-3 flex items-center justify-center gap-2">
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                className="h-5 w-5 text-blue-400" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
                />
              </svg>
              Need Help?
            </h3>
            <p className="text-gray-300 text-sm leading-relaxed">
              If you believe this is an error, please check the URL or contact support. 
              You can also visit our main dashboard to explore available features.
            </p>
          </div>
        </div>

        {/* Branding */}
        <div className="pt-8 fade-in-up delay-600">
          <p className="text-gray-500 text-sm">
            © {new Date().getFullYear()} BlocksConnect Panel
          </p>
        </div>
      </div>
    </div>
  );
}
