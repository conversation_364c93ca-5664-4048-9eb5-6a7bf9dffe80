'use client';

import { useState } from 'react';
import ProtectedRoute from '../../../components/ProtectedRoute';
import AdminHeader from '../../../components/Header';
import ServerForm from '../../../components/ServerForm';
import ServerList from '../../../components/ServerList';

export default function DashboardPage() {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleServerCreated = () => {
    // Increment the refresh trigger to cause the ServerList to refresh
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen text-white">
        <AdminHeader />

        <main className="container mx-auto py-12 px-4 max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
            {/* Server Creation Form */}
            <div className="lg:col-span-1 fade-in-up">
              <ServerForm onServerCreated={handleServerCreated} />
            </div>

            {/* Server List */}
            <div className="lg:col-span-2 fade-in-up delay-200">
              <div className="card overflow-hidden">
                <div className="px-8 py-6 border-b border-white/10 bg-gradient-to-r from-blue-500/10 to-purple-500/10">
                  <h2 className="text-2xl font-bold flex items-center gradient-text">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                    </svg>
                    Your Minecraft Servers
                  </h2>
                  <p className="text-gray-300 mt-2 font-light">Manage and monitor your server instances</p>
                </div>
                <div className="p-8">
                  <ServerList refreshTrigger={refreshTrigger} />
                </div>
              </div>
            </div>
          </div>
        </main>

        <footer className="relative mt-20">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-900/20 to-purple-900/20 backdrop-blur-sm"></div>
          <div className="relative z-10 container mx-auto text-center py-8 px-4">
            <div className="border-t border-white/10 pt-8">
              <p className="text-sm text-gray-300 font-medium">
               &copy; {new Date().getFullYear()} BlocksConnect
              </p>
            </div>
          </div>
        </footer>
      </div>
    </ProtectedRoute>
  );
}
