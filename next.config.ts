import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Keep your optimizations for package imports
  experimental: {
    optimizePackageImports: ['firebase/app', 'firebase/auth']
  },
  
  // Add output standalone mode
  output: 'standalone',
  
  // Move from experimental.serverComponentsExternalPackages to serverExternalPackages
  serverExternalPackages: ['firebase-admin'],
  
  // Keep your ESLint configuration
  eslint: {
    dirs: ['src'],
    ignoreDuringBuilds: true,
  },

  // Keep TypeScript error handling in development
  typescript: {
    ignoreBuildErrors: process.env.NODE_ENV === 'development',
  },

  // Keep console removal in production
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  
  // Disable static generation for pages that use Firebase
  // This prevents the prerendering error
  staticPageGenerationTimeout: 1000,
  
  // Add this to prevent static generation of authenticated routes
  reactStrictMode: true,
};

export default nextConfig;




