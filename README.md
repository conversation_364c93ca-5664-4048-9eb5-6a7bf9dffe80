# Minecraft Server Admin Panel

A modern, containerized web interface for managing Minecraft servers with Firebase Authentication. Features enterprise-grade security with Auth-only Firebase configuration and Docker containerization.

## Features

- **🔐 Firebase Authentication**: Enterprise-grade security with Google Sign-In and Email/Password
- **🐳 Docker Containerized**: Production-ready containerization with Docker Compose
- **🎮 Server Management**: Create, start, stop, and delete Minecraft servers
- **📊 Real-time Monitoring**: Live server status, player counts, and performance metrics
- **💾 Backup Management**: Automated backups with download functionality
- **🖥️ Server Console**: Interactive console for logs and commands
- **🔒 Auth-Only Firebase**: Minimal, secure Firebase setup excluding unnecessary services

## Quick Start Options

### 🐳 Docker (Recommended)

1. **Configure Environment**:
   ```bash
   cp .env.docker .env
   # Edit .env with your Firebase configuration
   ```

2. **Start with Docker Compose**:
   ```bash
   # Production mode
   docker-compose up -d

   # Development mode
   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
   ```

3. **Access the Application**:
   - Frontend: http://localhost:5003
   - Backend API: http://localhost:5000/api

### 💻 Local Development

1. **Install Dependencies**:
   ```bash
   npm install
   cd backend && pip install -r requirements.txt
   ```

2. **Configure Firebase** (see FIREBASE_SETUP.md):
   ```bash
   cp .env.local.example .env.local
   # Edit with your Firebase Auth-only configuration
   ```

3. **Start Services**:
   ```bash
   # Frontend
   npm run dev

   # Backend (separate terminal)
   cd backend && python app.py
   ```

## 🔐 Firebase Authentication

This application uses Firebase Authentication with an Auth-only configuration for enterprise-grade security:

### Supported Authentication Methods:
- **Google Sign-In**: OAuth 2.0 with Google's security infrastructure
- **Email/Password**: Secure password hashing and validation
- **JWT Token Verification**: Proper token handling and verification
- **Rate Limiting**: Built-in Firebase Auth rate limiting
- **Session Management**: Secure session handling with token refresh

### Setup:
1. Follow the [Firebase Setup Guide](FIREBASE_SETUP.md) for Auth-only configuration
2. Configure your environment variables with Firebase credentials
3. No default credentials needed - use your Firebase project users

## 📁 Documentation

- **[Firebase Setup Guide](FIREBASE_SETUP.md)**: Complete Firebase Auth-only configuration
- **[Docker Setup Guide](DOCKER_SETUP.md)**: Containerization and deployment
- **[PowerShell Helper](docker-helper.ps1)**: Docker management script

## Project Structure

```
web-panel/
├── src/
│   ├── app/                    # Next.js app directory
│   │   ├── dashboard/          # Dashboard page
│   │   ├── login/              # Login page
│   │   ├── server/[id]/        # Server details page
│   │   ├── globals.css         # Global styles
│   │   ├── layout.tsx          # Root layout
│   │   └── page.tsx            # Home page (redirects)
│   ├── components/             # Reusable components
│   │   ├── AdminHeader.tsx     # Admin navigation header
│   │   ├── ProtectedRoute.tsx  # Authentication wrapper
│   │   ├── ServerConsole.tsx   # Server console component
│   │   ├── ServerForm.tsx      # Server creation form
│   │   └── ServerList.tsx      # Server list component
│   ├── contexts/               # React contexts
│   │   └── AuthContext.tsx     # Authentication context
│   ├── lib/                    # Utility libraries
│   │   └── auth.ts             # Authentication utilities
│   └── services/               # API services
│       └── api.ts              # Backend API integration
├── package.json
├── tailwind.config.ts
├── tsconfig.json
└── README.md
```

## Key Differences from Main WebUI

### Removed Features:
- Landing page and marketing content
- Pricing page
- Welcome modal for new users
- Public navigation elements
- Multi-user support
- Plan-based restrictions

### Added Features:
- Simple login system
- Protected routes
- Admin-focused navigation
- Streamlined interface

## Authentication

The admin panel uses a simple authentication system:

- Login page as the entry point
- Session stored in localStorage
- Protected routes that redirect to login
- Logout functionality in the header

## API Integration

The admin panel uses the same backend API as the main webui:

- All server management endpoints remain unchanged
- Same data models and interfaces
- Compatible with existing Python backend

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Forms**: React Hook Form
- **Authentication**: Custom implementation with localStorage

## 🐳 Docker Management

### Using PowerShell Helper Script:
```powershell
# Start production environment
.\docker-helper.ps1 start

# Start development environment
.\docker-helper.ps1 dev

# View logs
.\docker-helper.ps1 logs -Follow

# Check health status
.\docker-helper.ps1 health

# Stop services
.\docker-helper.ps1 stop

# Clean up Docker resources
.\docker-helper.ps1 clean
```

### Manual Docker Commands:
```bash
# Production
docker-compose up -d

# Development
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 🚀 Production Deployment

### Docker (Recommended):
1. **Configure environment**: Edit `.env` with production Firebase credentials
2. **Deploy**: `docker-compose up -d`
3. **Monitor**: `docker-compose logs -f`

### Traditional Deployment:
1. **Build the application**: `npm run build`
2. **Start production server**: `npm run start`
3. **Configure environment**: Set production Firebase credentials

## 🔒 Security Features

### Firebase Auth-Only Configuration:
- ✅ **Google Sign-In**: OAuth 2.0 with Google's security infrastructure
- ✅ **Email/Password**: Secure password hashing and validation
- ✅ **JWT Token Verification**: Proper token handling and verification
- ✅ **Rate Limiting**: Built-in Firebase Auth rate limiting
- ✅ **CSRF Protection**: Implemented through proper token handling
- ✅ **Session Management**: Secure session handling with token refresh

### Docker Security:
- ✅ **Non-root containers**: Both services run as non-root users
- ✅ **Network isolation**: Services communicate via internal Docker network
- ✅ **Environment isolation**: Secrets managed through environment variables
- ✅ **Health checks**: Automated health monitoring for both services

### Production Recommendations:
- Use HTTPS with proper SSL certificates
- Configure Firebase authorized domains
- Set up proper logging and monitoring
- Regular security updates for dependencies
- Backup strategies for persistent data

## 🆘 Support & Troubleshooting

### Common Issues:
1. **Firebase Configuration**: Check FIREBASE_SETUP.md for Auth-only setup
2. **Docker Issues**: Use `docker-helper.ps1 health` to check service status
3. **Port Conflicts**: Ensure ports 5003 and 5000 are available
4. **Environment Variables**: Verify `.env` file has correct Firebase credentials

### Getting Help:
- Check service logs: `docker-compose logs -f`
- Verify Firebase configuration in Firebase Console
- Ensure Docker and Docker Compose are properly installed
- Review the documentation files for detailed setup instructions
