import type { Metadata } from "next";
import { Inter_Tight } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "../contexts/AuthContext";
import ParticleBackground from "../components/ParticleBackground";

const interTight = Inter_Tight({
  variable: "--font-inter-tight",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700", "900"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "BlocksConnect Admin Panel",
  description: "Admin interface for BlocksConnect services",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${interTight.variable} antialiased`}>
        <ParticleBackground />
        <div className="decorative-blob decorative-blob-1"></div>
        <div className="decorative-blob decorative-blob-2"></div>
        <div className="decorative-blob decorative-blob-3"></div>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
