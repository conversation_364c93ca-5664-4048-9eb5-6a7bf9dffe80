#!/usr/bin/env node

/**
 * Quick setup script for admin panel security
 * Run with: node setup-admin.js
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

console.log('🔐 Admin Panel Security Setup\n');

// Generate a secure JWT secret
const jwtSecret = crypto.randomBytes(32).toString('hex');

// Prompt for admin email
const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function setup() {
  try {
    console.log('Please provide the following information:\n');

    const adminEmail = await askQuestion('Enter your admin email address: ');
    const firebaseApiKey = await askQuestion('Enter your Firebase API key: ');
    const firebaseAuthDomain = await askQuestion('Enter your Firebase auth domain (e.g., project.firebaseapp.com): ');
    const firebaseProjectId = await askQuestion('Enter your Firebase project ID: ');
    const firebaseSenderId = await askQuestion('Enter your Firebase messaging sender ID: ');
    const firebaseAppId = await askQuestion('Enter your Firebase app ID: ');

    // Create .env.local content
    const envContent = `# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=${firebaseApiKey}
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=${firebaseAuthDomain}
NEXT_PUBLIC_FIREBASE_PROJECT_ID=${firebaseProjectId}
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=${firebaseSenderId}
NEXT_PUBLIC_FIREBASE_APP_ID=${firebaseAppId}

# Preview Access Configuration - CRITICAL: Only these emails can access the admin panel
# You can add multiple emails separated by commas
PREVIEW_ACCESS_EMAILS=${adminEmail}
NEXT_PUBLIC_PREVIEW_ACCESS_EMAILS=${adminEmail}

# Backward compatibility (deprecated - use PREVIEW_ACCESS_EMAILS instead)
ADMIN_EMAILS=${adminEmail}
NEXT_PUBLIC_ADMIN_EMAILS=${adminEmail}

# Domain Configuration
MAIN_DOMAIN=blocksconnect.com
PANEL_DOMAIN=panel.blocksconnect.com
NEXT_PUBLIC_MAIN_DOMAIN=blocksconnect.com

# Security
JWT_SECRET=${jwtSecret}
NEXTAUTH_SECRET=${jwtSecret}
NEXTAUTH_URL=https://panel.blocksconnect.com

# Development
NODE_ENV=development
`;

    // Write .env.local file
    fs.writeFileSync('.env.local', envContent);

    console.log('\n✅ Setup complete!');
    console.log('\n📁 Created .env.local with your configuration');
    console.log('\n🔑 Admin email configured:', adminEmail);
    console.log('\n🚀 Next steps:');
    console.log('1. Restart your development server: npm run dev');
    console.log('2. Visit http://localhost:3000/login');
    console.log('3. Sign in with your admin email');
    console.log('4. For production, copy these variables to your hosting platform');

    console.log('\n⚠️  Security Notes:');
    console.log('- Only the email', adminEmail, 'can access the admin panel');
    console.log('- Other users will be redirected to blocksconnect.com');
    console.log('- Keep your JWT_SECRET secure and never commit it to version control');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
  } finally {
    rl.close();
  }
}

setup();
