'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { getServices, PlatformService } from '../services/platform-api';
import Link from 'next/link';
import ProtectedRoute from '../components/ProtectedRoute';
import AdminHeader from '../components/Header';

export default function HomePage() {
  const [services, setServices] = useState<PlatformService[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user, isLoading: authLoading } = useAuth();

  useEffect(() => {
    const fetchServices = async () => {
      // Don't fetch if auth is still loading or user is not authenticated
      if (authLoading || !user) {
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const data = await getServices();
        setServices(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchServices();
  }, [user, authLoading]);

  const getServiceIcon = (iconName: string) => {
    switch (iconName) {
      case 'minecraft':
        return (
          <div className="w-16 h-16 bg-green-500 rounded-lg flex items-center justify-center text-white text-2xl font-bold">
            MC
          </div>
        );
      case 'files':
        return (
          <div className="w-16 h-16 bg-blue-500 rounded-lg flex items-center justify-center text-white text-2xl font-bold">
            📁
          </div>
        );
      default:
        return (
          <div className="w-16 h-16 bg-gray-500 rounded-lg flex items-center justify-center text-white text-2xl font-bold">
            ?
          </div>
        );
    }
  };

  if (authLoading || isLoading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen text-white">
          <AdminHeader />
          <div className="flex items-center justify-center py-20">
            <div className="text-center fade-in-up">
              <div className="relative mb-6">
                <div className="animate-spin h-16 w-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full mx-auto"></div>
                <div className="absolute inset-0 animate-ping h-16 w-16 border-4 border-blue-500/20 rounded-full mx-auto"></div>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                {authLoading ? 'Authenticating...' : 'Loading Services...'}
              </h3>
              <p className="text-gray-300 font-light">
                {authLoading ? 'Please wait while we verify your authentication...' : 'Please wait while we load available services...'}
              </p>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (error) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen text-white">
          <AdminHeader />
          <div className="flex items-center justify-center py-20">
            <div className="text-center fade-in-up">
              <div className="text-red-500 text-6xl mb-6">⚠️</div>
              <h3 className="text-xl font-semibold text-white mb-2">Error Loading Services</h3>
              <p className="text-gray-300 font-light mb-6">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen text-white">
        <AdminHeader />

        {/* Hero Section */}
        <div className="relative overflow-hidden">
          <div className="absolute inset-0"></div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div className="text-center fade-in-up">
              <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                Welcome back, <span className="text-blue-400 font-medium">{user?.displayName || user?.email || 'User'}</span>
              </h1>
            </div>
          </div>
        </div>

        {/* Services Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Your Available Services</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service) => (
              <div
                key={service.id}
                className={`relative group ${
                  service.enabled
                    ? 'hover:cursor-pointer'
                    : 'opacity-60 cursor-not-allowed'
                } transition-all duration-300 ease-out fade-in-up`}
              >
                <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 h-full">
                  {service.enabled && (
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-out"></div>
                  )}

                  <div className="relative z-10">
                    <div className="flex items-center mb-4">
                      {getServiceIcon(service.icon)}
                      <div className="ml-4">
                        <h3 className="text-xl font-semibold text-white">{service.name}</h3>
                        {!service.enabled && (
                          <span className="inline-block px-2 py-1 text-xs bg-yellow-500/20 text-yellow-400 rounded-full mt-1">
                            Coming Soon
                          </span>
                        )}
                      </div>
                    </div>

                    <br />

                    {/*<p className="text-gray-300 mb-6">{service.description}</p>*/}

                    {/*<div className="mb-6">
                      <h4 className="text-sm font-medium text-gray-400 mb-2">Features:</h4>
                      <ul className="space-y-1">
                        {service.features.map((feature, index) => (
                          <li key={index} className="text-sm text-gray-300 flex items-center">
                            <span className="text-green-400 mr-2">✓</span>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>*/}

                    {service.enabled ? (
                      <Link
                        href={service.path}
                        className="inline-flex items-center justify-center w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                      >
                        Access Service
                        <span className="ml-2">→</span>
                      </Link>
                    ) : (
                      <div className="inline-flex items-center justify-center w-full px-4 py-2 bg-gray-600 text-gray-300 rounded-lg cursor-not-allowed">
                        Coming Soon
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-800 mt-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center text-gray-400">
              <p>&copy; {new Date().getFullYear()} BlocksConnect. All rights reserved.</p>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}


