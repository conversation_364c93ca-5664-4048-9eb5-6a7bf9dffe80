'use client';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

// Configuration
const MAX_RETRY_ATTEMPTS = 3;
const INITIAL_RETRY_DELAY = 1000; // 1 second
const AUTH_ERROR_EVENT = 'auth-error'; // Custom event name for auth errors

export interface ServerProperties {
  [key: string]: string;
}

export interface PlayerInfo {
  online: number;
  max: number;
  list: string[];
}

export interface Server {
  id: string;
  name: string;
  port: number;
  version: string;
  memory: string;
  status: string;
  backup?: boolean;
  container_id?: string;
  properties?: ServerProperties;
  players?: PlayerInfo;
}

export interface ServerFormData {
  name: string;
  port: number;
  version: string;
  type: string;
  memory: string;
}

// Error class for authentication errors to distinguish them from other errors
export class AuthenticationError extends Error {
  status: number;
  
  constructor(message: string, status: number = 401) {
    super(message);
    this.name = 'AuthenticationError';
    this.status = status;
  }
}

// Helper function to emit auth error events for app-wide notification
export function emitAuthErrorEvent(error: AuthenticationError): void {
  if (typeof window !== 'undefined') {
    const event = new CustomEvent(AUTH_ERROR_EVENT, { 
      detail: { message: error.message, status: error.status } 
    });
    window.dispatchEvent(event);
  }
}

// Setup listener for auth error events
export function setupAuthErrorListener(callback: (error: { message: string, status: number }) => void): () => void {
  if (typeof window === 'undefined') return () => {};
  
  const listener = (event: Event) => {
    const customEvent = event as CustomEvent;
    callback(customEvent.detail);
  };
  
  window.addEventListener(AUTH_ERROR_EVENT, listener);
  return () => window.removeEventListener(AUTH_ERROR_EVENT, listener);
}

// Helper function to get authentication headers from a token
export function buildAuthHeaders(token: string | null): HeadersInit {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  return headers;
}

// Simple fetch wrapper (no hooks)
export async function authenticatedFetch<T>(
  url: string,
  options: RequestInit
): Promise<T> {
  const response = await fetch(url, options);
  if (response.ok) {
    if (response.headers.get('Content-Type')?.includes('application/json')) {
      return await response.json() as T;
    }
    return response as unknown as T;
  }
  // Handle authentication errors (401 Unauthorized)
  if (response.status === 401) {
    const errorData = await response.json().catch(() => ({ error: 'Authentication failed' }));
    const authError = new AuthenticationError(
      errorData.error || `Authentication failed: ${response.status} ${response.statusText}`,
      response.status
    );
    emitAuthErrorEvent(authError);
    throw authError;
  }
  // Handle other error responses
  const errorData = await response.json().catch(() => ({ error: `Request failed: ${response.status} ${response.statusText}` }));
  throw new Error(errorData.error || `Request failed: ${response.status} ${response.statusText}`);
}

export async function getServers(token: string | null): Promise<Server[]> {
  try {
    const headers = buildAuthHeaders(token);
    return await authenticatedFetch<Server[]>(`${API_URL}/minecraft/servers`, {
      method: 'GET',
      headers,
      cache: 'no-store',
    });
  } catch (error) {
    console.error('Error fetching servers:', error);
    if (error instanceof AuthenticationError) {
      console.error('Authentication error while fetching servers');
    }
    throw error;
  }
}

export async function createServer(data: ServerFormData, token: string | null): Promise<Server> {
  try {
    const headers = buildAuthHeaders(token);
    return await authenticatedFetch<Server>(`${API_URL}/minecraft/servers`, {
      method: 'POST',
      headers,
      body: JSON.stringify(data),
    });
  } catch (error) {
    console.error('Error creating server:', error);
    if (error instanceof AuthenticationError) {
      console.error('Authentication error while creating server');
    }
    throw error;
  }
}

export async function startServer(serverId: string, token: string | null): Promise<{ status: string; message: string }> {
  try {
    const headers = buildAuthHeaders(token);
    return await authenticatedFetch<{ status: string; message: string }>(`${API_URL}/minecraft/servers/${serverId}/start`, {
      method: 'POST',
      headers: {
        ...headers,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
    });
  } catch (error) {
    console.error(`Error starting server ${serverId}:`, error);
    if (error instanceof AuthenticationError) {
      console.error(`Authentication error while starting server ${serverId}`);
    }
    throw error;
  }
}

export async function stopServer(serverId: string, token: string | null): Promise<{ status: string; message: string }> {
  try {
    const headers = buildAuthHeaders(token);
    return await authenticatedFetch<{ status: string; message: string }>(`${API_URL}/minecraft/servers/${serverId}/stop`, {
      method: 'POST',
      headers: {
        ...headers,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
    });
  } catch (error) {
    console.error(`Error stopping server ${serverId}:`, error);
    if (error instanceof AuthenticationError) {
      console.error(`Authentication error while stopping server ${serverId}`);
    }
    throw error;
  }
}

export async function deleteServer(serverId: string, token: string | null): Promise<{ message: string }> {
  try {
    const headers = buildAuthHeaders(token);
    return await authenticatedFetch<{ message: string }>(`${API_URL}/minecraft/servers/${serverId}`, {
      method: 'DELETE',
      headers: {
        ...headers,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
    });
  } catch (error) {
    console.error(`Error deleting server ${serverId}:`, error);
    if (error instanceof AuthenticationError) {
      console.error(`Authentication error while deleting server ${serverId}`);
    }
    throw error;
  }
}

export async function toggleBackup(serverId: string, token: string | null): Promise<{ message: string }> {
  try {
    const headers = buildAuthHeaders(token);
    return await authenticatedFetch<{ message: string }>(`${API_URL}/minecraft/servers/${serverId}/backup`, {
      method: 'POST',
      headers: {
        ...headers,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
    });
  } catch (error) {
    console.error(`Error toggling backup for server ${serverId}:`, error);
    if (error instanceof AuthenticationError) {
      console.error(`Authentication error while toggling backup for server ${serverId}`);
    }
    throw error;
  }
}

export async function downloadBackup(serverId: string, token: string | null): Promise<void> {
  try {
    const headers = buildAuthHeaders(token);
    const response = await authenticatedFetch<Response>(`${API_URL}/minecraft/servers/${serverId}/backup`, {
      method: 'GET',
      headers,
    });
    if (response instanceof Response) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `server_${serverId}_backup.zip`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } else {
      throw new Error('Unexpected response type');
    }
  } catch (error) {
    console.error(`Error downloading backup for server ${serverId}:`, error);
    if (error instanceof AuthenticationError) {
      console.error(`Authentication error while downloading backup for server ${serverId}`);
    }
    throw error;
  }
}

export async function getServerDetails(serverId: string, token: string | null): Promise<Server> {
  try {
    const headers = buildAuthHeaders(token);
    return await authenticatedFetch<Server>(`${API_URL}/minecraft/servers/${serverId}`, {
      method: 'GET',
      headers,
      cache: 'no-store',
    });
  } catch (error) {
    console.error(`Error fetching server details for ${serverId}:`, error);
    if (error instanceof AuthenticationError) {
      console.error(`Authentication error while fetching server details for ${serverId}`);
    }
    throw error;
  }
}

export async function getServerLogs(serverId: string, token: string | null): Promise<{ logs: string }> {
  try {
    const headers = buildAuthHeaders(token);
    return await authenticatedFetch<{ logs: string }>(`${API_URL}/minecraft/servers/${serverId}/logs`, {
      method: 'GET',
      headers,
      cache: 'no-store',
    });
  } catch (error) {
    console.error(`Error fetching logs for server ${serverId}:`, error);
    if (error instanceof AuthenticationError) {
      console.error(`Authentication error while fetching logs for server ${serverId}`);
    }
    throw error;
  }
}

export async function sendServerCommand(serverId: string, command: string, token: string | null): Promise<{ response: string }> {
  try {
    const headers = buildAuthHeaders(token);
    return await authenticatedFetch<{ response: string }>(`${API_URL}/minecraft/servers/${serverId}/command`, {
      method: 'POST',
      headers,
      body: JSON.stringify({ command }),
    });
  } catch (error) {
    console.error(`Error sending command to server ${serverId}:`, error);
    if (error instanceof AuthenticationError) {
      console.error(`Authentication error while sending command to server ${serverId}`);
    }
    throw error;
  }
}

// Enhanced helper function to handle auth errors at the component level
export function handleApiAuthError(error: unknown, onAuthError?: () => void): boolean {
  if (error instanceof AuthenticationError) {
    console.error('Authentication error occurred:', error.message);
    
    // Emit auth error event
    emitAuthErrorEvent(error);
    
    // If a callback is provided, execute it
    if (onAuthError) {
      onAuthError();
    }
    
    return true;
  }
  return false;
}
