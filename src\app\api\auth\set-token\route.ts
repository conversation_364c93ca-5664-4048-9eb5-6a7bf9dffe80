import { NextRequest, NextResponse } from 'next/server';
import { SignJWT } from 'jose';
import { cookies } from 'next/headers';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const ADMIN_EMAILS = (process.env.ADMIN_EMAILS || '').split(',').map(email => email.trim());

// Simple Firebase token decoder (in production, use Firebase Admin SDK)
function decodeFirebaseToken(token: string) {
  try {
    // This is a simplified version - in production, use Firebase Admin SDK to verify
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload;
  } catch (error) {
    throw new Error('Invalid token format');
  }
}

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json({ error: 'Token is required' }, { status: 400 });
    }

    // Decode Firebase token to get user email
    let userEmail: string;
    try {
      const payload = decodeFirebaseToken(token);
      userEmail = payload.email;

      if (!userEmail) {
        return NextResponse.json({ error: 'No email in token' }, { status: 400 });
      }
    } catch (error) {
      return NextResponse.json({ error: 'Invalid Firebase token' }, { status: 400 });
    } 

    // Check if user email is in admin whitelist
    if (!ADMIN_EMAILS.includes(userEmail)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Create a JWT token for our application
    const secret = new TextEncoder().encode(JWT_SECRET);
    const jwt = await new SignJWT({
      email: userEmail,
      firebaseToken: token,
      isAdmin: true
    })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime('24h')
      .sign(secret);

    // Set HTTP-only cookie
    const cookieStore = await cookies();
    cookieStore.set('admin-token', jwt, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60, // 24 hours
      path: '/',
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Set token error:', error);
    return NextResponse.json({ error: 'Failed to set token' }, { status: 500 });
  }
}
