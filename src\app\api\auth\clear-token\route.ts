import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST() {
  try {
    // Clear the HTTP-only cookie
    const cookieStore = await cookies();
    cookieStore.delete('admin-token');

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Clear token error:', error);
    return NextResponse.json({ error: 'Failed to clear token' }, { status: 500 });
  }
}
