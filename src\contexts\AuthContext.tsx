'use client';

import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { User } from 'firebase/auth';
import {
  onAuthStateChange,
  logOut,
  getIdToken,
  signInWithEmail,
  signInWithGoogle,
  signUpWithEmail
} from '../lib/firebase';
import {
  createAuthUser,
  hasPreviewAccess,
  setAuthToken,
  clearAuthToken,
  redirectToMainSite,
  AuthUser
} from '../lib/auth';

interface AuthContextType {
  user: User | null;
  authUser: AuthUser | null;
  isLoading: boolean;
  isAdmin: boolean;
  tokenSyncError: string | null;
  signIn: (email: string, password: string) => Promise<{ user: User | null; error: string | null }>;
  signUp: (email: string, password: string, name: string) => Promise<{ user: User | null; error: string | null }>;
  signInWithGoogle: () => Promise<{ user: User | null; error: string | null }>;
  logout: () => Promise<{ error: string | null }>;
  getToken: (forceRefresh?: boolean) => Promise<string | null>;
  refreshToken: () => Promise<string | null>;
  recoverFromTokenError: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [authUser, setAuthUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [tokenSyncError, setTokenSyncError] = useState<string | null>(null);
  const tokenRefreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const tokenRetryCountRef = useRef(0);
  const MAX_RETRY_COUNT = 3;
  const TOKEN_REFRESH_INTERVAL = 15 * 60 * 1000; // 15 minutes

  // Function to synchronize tokens - ensures both Firebase and server tokens are in sync
  const synchronizeTokens = async (forceRefresh = true, retryCount = 0): Promise<string | null> => {
    if (!user) {
      return null;
    }
    
    try {
      // Get fresh Firebase ID token
      const idToken = await getIdToken(forceRefresh);
      
      if (idToken) {
        // Update server-side token
        await setAuthToken(idToken);
        setTokenSyncError(null);
        tokenRetryCountRef.current = 0;
        return idToken;
      } else {
        throw new Error('Failed to get ID token');
      }
    } catch (error) {
      console.error('Token synchronization failed:', error);
      setTokenSyncError(`Token sync error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      // Implement retry logic if needed
      if (retryCount < MAX_RETRY_COUNT) {
        console.log(`Retrying token sync (${retryCount + 1}/${MAX_RETRY_COUNT})...`);
        // Exponential backoff: wait longer between retries
        const backoffTime = Math.pow(2, retryCount) * 1000;
        await new Promise(resolve => setTimeout(resolve, backoffTime));
        return synchronizeTokens(forceRefresh, retryCount + 1);
      }
      
      tokenRetryCountRef.current = retryCount;
      return null;
    }
  };

  // Set up token refresh interval
  useEffect(() => {
    const setupTokenRefresh = async () => {
      // Clear any existing refresh timeout
      if (tokenRefreshTimeoutRef.current) {
        clearTimeout(tokenRefreshTimeoutRef.current);
        tokenRefreshTimeoutRef.current = null;
      }

      // Only set up refresh for authenticated users
      if (user) {
        tokenRefreshTimeoutRef.current = setTimeout(async () => {
          console.log('Performing scheduled token refresh');
          await synchronizeTokens(true);
          // Set up next refresh
          setupTokenRefresh();
        }, TOKEN_REFRESH_INTERVAL);
      }
    };

    setupTokenRefresh();

    // Cleanup on unmount
    return () => {
      if (tokenRefreshTimeoutRef.current) {
        clearTimeout(tokenRefreshTimeoutRef.current);
      }
    };
  }, [user]);

  useEffect(() => {
    // Listen for authentication state changes
    const unsubscribe = onAuthStateChange(async (firebaseUser) => {
      setUser(firebaseUser);

      if (firebaseUser) {
        // Check if user has preview access
        if (!hasPreviewAccess(firebaseUser.email || '')) {
          // User is not authorized, redirect to main site
          console.warn('Unauthorized preview access attempt:', firebaseUser.email);
          await logOut();
          redirectToMainSite();
          return;
        }

        // Create auth user and set server-side token
        const newAuthUser = createAuthUser(firebaseUser);
        setAuthUser(newAuthUser);

        try {
          // Synchronize Firebase and server-side tokens
          await synchronizeTokens(true);
        } catch (error) {
          console.error('Failed to set auth token during auth state change:', error);
        }
      } else {
        // User logged out
        setAuthUser(null);
        await clearAuthToken();
        
        // Clear any token refresh timeout
        if (tokenRefreshTimeoutRef.current) {
          clearTimeout(tokenRefreshTimeoutRef.current);
          tokenRefreshTimeoutRef.current = null;
        }
      }

      setIsLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const result = await signInWithEmail(email, password);
      return result;
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (email: string, password: string, name: string) => {
    setIsLoading(true);
    try {
      const result = await signUpWithEmail(email, password, name);
      return result;
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithGoogleProvider = async () => {
    setIsLoading(true);
    try {
      const result = await signInWithGoogle();
      return result;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      // Clear server-side token first
      await clearAuthToken();

      // Then logout from Firebase
      const result = await logOut();

      // Clear local state
      setAuthUser(null);

      return result;
    } finally {
      setIsLoading(false);
    }
  };

  const getToken = async (forceRefresh = false) => {
    // Prefer synchronized tokens to ensure consistency
    try {
      // If forcing refresh, use synchronizeTokens to ensure both Firebase and server tokens are updated
      if (forceRefresh) {
        return await synchronizeTokens(true);
      }
      
      // For non-forced refreshes, we can use the cached token if available
      return await getIdToken(false);
    } catch (error) {
      console.error('Error getting token:', error);
      return null;
    }
  };

  const refreshToken = async () => {
    // Always use synchronizeTokens for refreshes to ensure both tokens are updated
    return await synchronizeTokens(true);
  };
  
  // This function can be called to recover from token sync errors
  const recoverFromTokenError = async (): Promise<boolean> => {
    if (tokenSyncError && user) {
      try {
        const token = await synchronizeTokens(true);
        return !!token;
      } catch (error) {
        console.error('Failed to recover from token error:', error);
        return false;
      }
    }
    return false;
  };

  const value = {
    user,
    authUser,
    isLoading,
    isAdmin: authUser?.isAdmin || false,
    signIn,
    signUp,
    signInWithGoogle: signInWithGoogleProvider,
    logout,
    getToken,
    refreshToken,
    tokenSyncError,
    recoverFromTokenError
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
