# ============================================================================
# BlocksConnect Web Panel - Development Dockerfile
# ============================================================================

FROM node:20-alpine AS development

# Install dependencies for development
RUN apk add --no-cache libc6-compat curl

WORKDIR /app

# Create a non-root user for development
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy package files
COPY package.json package-lock.json* ./

# Install all dependencies (including dev dependencies)
RUN npm ci

# Change ownership of the app directory
RUN chown -R nextjs:nodejs /app

USER nextjs

# Copy the rest of the application
COPY --chown=nextjs:nodejs . .

EXPOSE 3000

ENV NODE_ENV development
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# Start the development server
CMD ["npm", "run", "dev"]
