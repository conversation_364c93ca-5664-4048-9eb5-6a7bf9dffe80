import { NextRequest, NextResponse } from 'next/server';
import { jwtVerify } from 'jose';
import { cookies } from 'next/headers';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Preview access email whitelist - supports multiple admin emails
const getPreviewAccessEmails = (): string[] => {
  // Try new naming convention first, fallback to old for backward compatibility
  const previewEmails = process.env.PREVIEW_ACCESS_EMAILS || process.env.ADMIN_EMAILS || '';
  return previewEmails.split(',')
    .map(email => email.trim().toLowerCase())
    .filter(email => email.length > 0 && isValidEmail(email));
};

// Validate email format
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const PREVIEW_ACCESS_EMAILS = getPreviewAccessEmails();

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get('admin-token')?.value;

    if (!token) {
      return NextResponse.json({ authenticated: false, error: 'No token found' }, { status: 401 });
    }

    // Verify JWT token
    const secret = new TextEncoder().encode(JWT_SECRET);
    const { payload } = await jwtVerify(token, secret);

    // Extract user email from the payload
    const userEmail = (payload.email as string)?.toLowerCase();

    // Check if user email has preview access
    if (!userEmail || !PREVIEW_ACCESS_EMAILS.includes(userEmail)) {
      console.warn(`Unauthorized access attempt from email: ${userEmail}`);
      return NextResponse.json({
        authenticated: false,
        error: 'User not authorized for preview access',
        shouldRedirect: true
      }, { status: 403 });
    }

    return NextResponse.json({
      authenticated: true,
      user: {
        email: userEmail,
        isAdmin: true
      }
    });
  } catch (error) {
    console.error('Token verification failed:', error);
    return NextResponse.json({ authenticated: false, error: 'Invalid token' }, { status: 401 });
  }
}
