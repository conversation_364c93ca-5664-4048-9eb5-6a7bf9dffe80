// Firebase configuration and initialization
import { initializeApp, getApps, FirebaseApp } from 'firebase/app';
import {
  getAuth,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged,
  User,
  updateProfile,
  updateEmail,
  updatePassword,
  Auth
} from 'firebase/auth';

// Only initialize Firebase on the client side
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Initialize Firebase only in browser environment
let app: FirebaseApp | null = null;
let auth: Auth | null = null;
let googleProvider: GoogleAuthProvider | null = null;

// Only initialize Firebase in browser environment
if (typeof window !== 'undefined') {
  try {
    // Validate required config values
    const requiredFields = ['apiKey', 'authDomain', 'projectId', 'appId'];
    const missingFields = requiredFields.filter(field => !firebaseConfig[field as keyof typeof firebaseConfig]);

    if (missingFields.length > 0) {
      console.error('Missing Firebase configuration fields:', missingFields);
      throw new Error(`Missing Firebase configuration: ${missingFields.join(', ')}`);
    }

    app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
    auth = getAuth(app);

    // Configure Google Auth Provider
    googleProvider = new GoogleAuthProvider();
    googleProvider.setCustomParameters({
      prompt: 'select_account'
    });

    console.log('Firebase initialized successfully');
  } catch (error) {
    console.error('Firebase initialization error:', error);
    // Don't throw here to prevent app crash, but log the error
  }
}

// Auth functions
export const signInWithEmail = async (email: string, password: string) => {
  if (!auth) throw new Error('Auth is not initialized');

  try {
    console.log('Attempting email/password sign-in for:', email);
    const result = await signInWithEmailAndPassword(auth, email, password);
    console.log('Email sign-in successful:', result.user?.email);
    return { user: result.user, error: null };
  } catch (error: any) {
    console.error('Email sign-in error:', error);
    console.error('Error code:', error.code);

    // Provide more specific error messages
    let userFriendlyMessage = error.message;
    if (error.code === 'auth/user-not-found') {
      userFriendlyMessage = 'No account found with this email address.';
    } else if (error.code === 'auth/wrong-password') {
      userFriendlyMessage = 'Incorrect password. Please try again.';
    } else if (error.code === 'auth/invalid-email') {
      userFriendlyMessage = 'Invalid email address format.';
    } else if (error.code === 'auth/too-many-requests') {
      userFriendlyMessage = 'Too many failed attempts. Please try again later.';
    }

    return { user: null, error: userFriendlyMessage };
  }
};

export const signUpWithEmail = async (email: string, password: string, name: string) => {
  if (!auth) throw new Error('Auth is not initialized');

  try {
    const result = await createUserWithEmailAndPassword(auth, email, password);
    // Set the displayName after account creation
    if (auth.currentUser && name) {
      await updateProfile(auth.currentUser, { displayName: name });
    }
    return { user: result.user, error: null };
  } catch (error: any) {
    return { user: null, error: error.message };
  }
};

export const signInWithGoogle = async () => {
  if (!auth || !googleProvider) throw new Error('Auth is not initialized');

  try {
    console.log('Attempting Google sign-in...');
    const result = await signInWithPopup(auth, googleProvider);
    console.log('Google sign-in successful:', result.user?.email);
    return { user: result.user, error: null };
  } catch (error: any) {
    console.error('Google sign-in error:', error);
    console.error('Error code:', error.code);
    console.error('Error message:', error.message);

    // Provide more specific error messages
    let userFriendlyMessage = error.message;
    if (error.code === 'auth/popup-blocked') {
      userFriendlyMessage = 'Popup was blocked. Please allow popups for this site and try again.';
    } else if (error.code === 'auth/popup-closed-by-user') {
      userFriendlyMessage = 'Sign-in was cancelled. Please try again.';
    } else if (error.code === 'auth/unauthorized-domain') {
      userFriendlyMessage = 'This domain is not authorized for Google sign-in. Please contact support.';
    }

    return { user: null, error: userFriendlyMessage };
  }
};

export const logOut = async () => {
  if (!auth) throw new Error('Auth is not initialized');

  try {
    await signOut(auth);
    return { error: null };
  } catch (error: any) {
    return { error: error.message };
  }
};

export const getCurrentUser = (): Promise<User | null> => {
  if (!auth) return Promise.resolve(null);

  return new Promise((resolve) => {
    const unsubscribe = onAuthStateChanged(auth!, (user) => {
      unsubscribe();
      resolve(user);
    });
  });
};

export const getIdToken = async (forceRefresh = false): Promise<string | null> => {
  if (!auth) return null;

  try {
    const user = auth.currentUser;
    if (user) {
      return await user.getIdToken(forceRefresh);
    }
    return null;
  } catch (error) {
    console.error('Error getting ID token:', error);
    return null;
  }
};

/**
 * Updates a user's profile with only the changed fields
 * @param updates Object containing the fields to update
 * @returns Promise that resolves when the update is complete
 */
export async function updateUserProfile(updates: {
  displayName?: string;
  email?: string;
  password?: string;
  photoURL?: string;
}): Promise<void> {
  if (!auth) throw new Error('Auth is not initialized');

  const user = auth.currentUser;

  if (!user) {
    throw new Error("No user is signed in");
  }

  const updatePromises: Promise<void>[] = [];

  // Only update fields that are provided and different from current values
  if (updates.displayName !== undefined && updates.displayName !== user.displayName) {
    updatePromises.push(updateProfile(user, { displayName: updates.displayName }));
  }

  if (updates.photoURL !== undefined && updates.photoURL !== user.photoURL) {
    updatePromises.push(updateProfile(user, { photoURL: updates.photoURL }));
  }

  if (updates.email !== undefined && updates.email !== user.email) {
    updatePromises.push(updateEmail(user, updates.email));
  }

  if (updates.password !== undefined) {
    updatePromises.push(updatePassword(user, updates.password));
  }

  // If no changes, return immediately
  if (updatePromises.length === 0) {
    return;
  }

  // Wait for all updates to complete
  await Promise.all(updatePromises);

  // Force token refresh to ensure the latest user data is available
  await user.getIdToken(true);
}

// Auth state listener
export const onAuthStateChange = (callback: (user: User | null) => void) => {
  if (!auth) {
    // If auth is not initialized, immediately call the callback with null
    callback(null);
    // Return a no-op function as the unsubscribe function
    return () => {};
  }

  return onAuthStateChanged(auth, callback);
};

// Export auth instance
export { auth };
export default app;