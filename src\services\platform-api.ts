'use client';

import { getIdToken } from '../lib/firebase';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

// Platform service interface
export interface PlatformService {
  id: string;
  name: string;
  description: string;
  icon: string;
  path: string;
  enabled: boolean;
  features: string[];
}

// Helper function to get authentication headers
async function getAuthHeaders(): Promise<HeadersInit> {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  try {
    const token = await getIdToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
  } catch (error) {
    console.error('Failed to get authentication token:', error);
  }

  return headers;
}

// Platform API functions
export async function getServices(): Promise<PlatformService[]> {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_URL}/services`, {
      method: 'GET',
      headers,
      cache: 'no-store',
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch services: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.services;
  } catch (error) {
    console.error('Error fetching services:', error);
    throw error;
  }
}

export async function getUserProfile(): Promise<any> {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_URL}/profile`, {
      method: 'GET',
      headers,
      cache: 'no-store',
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch profile: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching profile:', error);
    throw error;
  }
}

