import { NextRequest, NextResponse } from 'next/server';
import { jwtVerify } from 'jose';

// Configuration
const MAIN_DOMAIN = process.env.MAIN_DOMAIN || 'blocksconnect.com';
const PANEL_DOMAIN = process.env.PANEL_DOMAIN || 'panel.blocksconnect.com';
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Preview access email whitelist - supports multiple admin emails
const getPreviewAccessEmails = (): string[] => {
  // Try new naming convention first, fallback to old for backward compatibility
  const previewEmails = process.env.PREVIEW_ACCESS_EMAILS || process.env.ADMIN_EMAILS || '';
  return previewEmails.split(',')
    .map(email => email.trim().toLowerCase())
    .filter(email => email.length > 0 && isValidEmail(email));
};

// Validate email format
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const PREVIEW_ACCESS_EMAILS = getPreviewAccessEmails();

export async function middleware(request: NextRequest) {
  const { pathname, hostname } = request.nextUrl;

  // Only apply middleware to panel domain
  if (hostname !== PANEL_DOMAIN && !hostname.includes('localhost')) {
    return NextResponse.next();
  }

  // Allow access to login page and API routes
  if (pathname === '/login' || pathname.startsWith('/api/auth') || pathname.startsWith('/_next')) {
    return NextResponse.next();
  }

  // Check for authentication token
  const token = request.cookies.get('admin-token')?.value;

  if (!token) {
    // Redirect to login if no token
    return NextResponse.redirect(new URL('/login', request.url));
  }

  try {
    // Verify JWT token
    const secret = new TextEncoder().encode(JWT_SECRET);
    const { payload } = await jwtVerify(token, secret);

    // Check if user email has preview access
    const userEmail = (payload.email as string)?.toLowerCase();
    if (!userEmail || !PREVIEW_ACCESS_EMAILS.includes(userEmail)) {
      console.warn(`Unauthorized access attempt from email: ${userEmail}`);
      // Redirect unauthorized users to main domain
      return NextResponse.redirect(`https://${MAIN_DOMAIN}`);
    }

    // User is authenticated and authorized
    return NextResponse.next();
  } catch (error) {
    console.error('Token verification failed:', error);

    // Clear invalid token and redirect to login
    const response = NextResponse.redirect(new URL('/login', request.url));
    response.cookies.delete('admin-token');
    return response;
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (authentication API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico).*)',
  ],
};
