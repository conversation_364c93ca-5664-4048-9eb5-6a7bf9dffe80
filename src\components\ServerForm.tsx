'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { createServer, ServerFormData } from '../services/api';
import { useAuth } from '@/contexts/AuthContext';

// Define common Minecraft versions
const MINECRAFT_VERSIONS = [
  'latest',
  '1.21.5',
  '1.21.4',
  '1.20.6',
  '1.20.4',
  '1.19.4',
  '1.19.2',
  '1.18.2',
  '1.17.1',
  '1.16.5',
  '1.15.2',
  '1.14.4',
  '1.12.2'
];

// Define memory options
const MEMORY_OPTIONS = [
  '1G',
  '2G',
  '4G',
  '6G',
  '8G',
];

const TYPE_OPTIONS = [
  'Vanilla',
  'Paper',
  'Fabric',
  'Forge'
];

interface ServerFormProps {
  onServerCreated: () => void;
}

export default function ServerForm({ onServerCreated }: ServerFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { getToken } = useAuth()

  const { register, handleSubmit, reset, formState: { errors } } = useForm<ServerFormData>({
    defaultValues: {
      version: MINECRAFT_VERSIONS[0], // Default to the first version in the list
      memory: '2G', // Default to 4G of memory
      type: 'vanilla'
    }
  });

  const onSubmit = async (data: ServerFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      const token = await getToken()
      await createServer(data, token);
      reset();
      onServerCreated();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="card overflow-hidden">
      <div className="px-8 py-6 border-b border-white/10 bg-gradient-to-r from-blue-500/10 to-purple-500/10">
        <h2 className="text-2xl font-bold flex items-center gradient-text">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Create New Server
        </h2>
        <p className="text-gray-300 mt-2 font-light">Configure and deploy a new Minecraft server</p>
      </div>

      <div className="p-8">
        {error && (
          <div className="bg-red-500/10 border border-red-500/30 text-red-400 px-4 py-3 rounded-lg mb-6 flex items-start backdrop-blur-sm">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <span>{error}</span>
          </div>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div>
            <label className="block text-gray-200 text-sm font-semibold mb-3" htmlFor="name">
              Server Name
            </label>
            <input
              className="input-field w-full"
              id="name"
              type="text"
              placeholder="My Minecraft Server"
              {...register('name', { required: 'Server name is required' })}
            />
            {errors.name && (
              <p className="text-red-400 text-sm mt-2 flex items-center">
                <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {errors.name.message}
              </p>
            )}
          </div>

          <div>
            <label className="block text-gray-200 text-sm font-semibold mb-3" htmlFor="port">
              Port
            </label>
            <input
              className="input-field w-full"
              id="port"
              type="number"
              placeholder="25565"
              {...register('port', {
                required: 'Port is required',
                min: { value: 1024, message: 'Port must be at least 1024' },
                max: { value: 65535, message: 'Port must be at most 65535' }
              })}
            />
            {errors.port && (
              <p className="text-red-400 text-sm mt-2 flex items-center">
                <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {errors.port.message}
              </p>
            )}
          </div>

          <div>
            <label className="block text-gray-200 text-sm font-semibold mb-3" htmlFor="version">
              Minecraft Version
            </label>
            <select
              className="input-field w-full"
              id="version"
              {...register('version', { required: 'Version is required' })}
            >
              <option value="" disabled>Select a Minecraft version</option>
              <option value={MINECRAFT_VERSIONS[0]}>{MINECRAFT_VERSIONS[0]} (Latest)</option>
              {MINECRAFT_VERSIONS.filter(version => version !== MINECRAFT_VERSIONS[0]).map((version) => (
                <option key={version} value={version}>
                  {version}
                </option>
              ))}
            </select>
            {errors.version && (
              <p className="text-red-400 text-sm mt-2 flex items-center">
                <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {errors.version.message}
              </p>
            )}
          </div>

          <div>
            <label className="block text-gray-200 text-sm font-semibold mb-3" htmlFor="type">
              Minecraft server software type
            </label>
            <select
              className="input-field w-full"
              id="type"
              {...register('type', { required: 'Server type is required' })}
            >
              <option value="" disabled>Select a Minecraft version</option>
              <option value={TYPE_OPTIONS[0]}>{TYPE_OPTIONS[0]}</option>
              {TYPE_OPTIONS.filter(type => type !== TYPE_OPTIONS[0]).map((type) => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </select>
            {errors.type && (
              <p className="text-red-400 text-sm mt-2 flex items-center">
                <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {errors.type.message}
              </p>
            )}
          </div>

          <div>
            <label className="block text-gray-200 text-sm font-semibold mb-3" htmlFor="memory">
              Memory Allocation
            </label>
            <select
              className="input-field w-full"
              id="memory"
              {...register('memory', { required: 'Memory is required' })}
            >
              <option value="" disabled>Select memory allocation</option>
              {MEMORY_OPTIONS.map((memory) => (
                <option key={memory} value={memory}>
                  {memory}
                </option>
              ))}
            </select>
            {errors.memory && (
              <p className="text-red-400 text-sm mt-2 flex items-center">
                <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {errors.memory.message}
              </p>
            )}
          </div>

          <div className="pt-4">
            <button
              className="btn-primary w-full py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              type="submit"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating Server...
                </div>
              ) : (
                <span className="flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                  Create Server
                </span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
